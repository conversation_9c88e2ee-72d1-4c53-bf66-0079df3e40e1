# Detailed Item Cache System

This document describes the intuitive and simple cache system implemented for storing detailed items with short TTL.

## Overview

The cache system provides transparent caching for detailed item data to improve performance when users repeatedly view the same items. It uses Redis for storage with configurable TTL and graceful fallback on cache failures.

## Components

### 1. DetailedItemCache (`src/lib/services/DetailedItemCache.server.ts`)

The core cache service that handles:

- **Storage**: Redis-based storage with configurable TTL
- **Retrieval**: Fast lookup with automatic expiration
- **Invalidation**: Individual item and device-wide cache clearing
- **Graceful Fallback**: Continues working even if Redis fails
- **Configuration**: Configurable TTL, enable/disable, and key prefixes

### 2. CachedBridgeClient (`src/lib/client/bridge/cached-bridge-client.ts`)

A transparent wrapper around the bridge client that:

- **Intercepts** `getDetailedItem` calls
- **Checks cache first** before making API calls
- **Stores results** in cache for future requests
- **Maintains compatibility** with the existing IBridgeClient interface
- **Provides cache management** methods for advanced use cases

### 3. Server-side API Caching (`src/routes/api/bridge/[deviceId]/item/online/[itemId]/+server.ts`)

The API route also implements caching to provide benefits even for direct API calls:

- **Cache-first approach** for all detailed item requests
- **Automatic cache population** after API calls
- **Graceful degradation** when cache is unavailable

**Note**: Server-side caching uses device IDs as cache keys (for compatibility with existing API structure), while client-side caching uses restaurant names (for better organization and consistency with URLs). Both approaches provide the same performance benefits.

## Configuration

Cache behavior is controlled through environment variables:

```bash
# Enable/disable caching (default: true)
PUBLIC_CACHE_ENABLED=true

# Cache TTL in seconds (default: 300 = 5 minutes)
PUBLIC_CACHE_TTL_SECONDS=300
```

### Recommended TTL Values

- **Development**: 60 seconds (1 minute) - for quick testing
- **Staging**: 300 seconds (5 minutes) - balanced performance/freshness
- **Production**: 600 seconds (10 minutes) - optimal performance

## Usage

### Basic Usage (Automatic)

The cache works transparently once integrated. No code changes are needed:

```typescript
// This automatically uses caching
const detailedItem = await bridgeClient.getDetailedItem(itemId);
```

### Advanced Cache Management

For advanced use cases, you can access cache management methods:

```typescript
// Get cache statistics
const stats = await cachedClient.getCacheStats();
console.log(`Total cached items: ${stats.totalKeys}`);

// Invalidate a specific item
await cachedClient.invalidateItem(itemId);

// Clear all cached items for this device
await cachedClient.invalidateAll();
```

### Direct Cache Access

For server-side operations, you can use the cache directly:

```typescript
import { detailedItemCache } from "$lib/services/DetailedItemCache.server.ts";

// Check if item is cached
const cachedItem = await detailedItemCache.get(restaurantName, itemId);

// Store item in cache
await detailedItemCache.set(restaurantName, itemId, detailedItem);

// Invalidate specific item
await detailedItemCache.invalidate(restaurantName, itemId);

// Invalidate all items for a restaurant
await detailedItemCache.invalidateRestaurant(restaurantName);
```

## Cache Key Structure

Cache keys follow a predictable pattern:

```
{keyPrefix}:{restaurantName}:{itemId}
```

Default example:

```
detailed_item:pizza-palace:item-456
```

This ensures:

- **Isolation** between different restaurants
- **Easy identification** of cached items using human-readable restaurant names
- **Efficient pattern matching** for bulk operations
- **Consistency** with URL structure (restaurant names from URLs)

## Error Handling

The cache system is designed to be resilient:

1. **Redis Connection Failures**: Cache operations fail gracefully, application continues normally
2. **Serialization Errors**: Invalid data is ignored, fresh data is fetched
3. **TTL Expiration**: Expired items are automatically removed by Redis
4. **Configuration Errors**: Invalid settings fall back to safe defaults

## Performance Benefits

- **Reduced API Calls**: Frequently accessed items are served from cache
- **Faster Response Times**: Redis lookup is much faster than API calls
- **Lower Server Load**: Fewer requests to the bridge API
- **Better User Experience**: Instant loading for previously viewed items

## Monitoring

Cache performance can be monitored through:

1. **Cache Statistics**: Use `getCacheStats()` to see hit/miss ratios
2. **Log Messages**: Cache operations are logged at trace level
3. **Redis Metrics**: Monitor Redis memory usage and key counts

## Testing

The cache system includes comprehensive tests:

- **Unit Tests**: `src/unit-tests/detailed-item-cache.test.ts`
- **Integration Tests**: `src/unit-tests/cache-integration.test.ts`
- **Bridge Client Tests**: `src/unit-tests/cached-bridge-client.test.ts`

Run tests with:

```bash
npm run test:unit
```

## Implementation Notes

- **Memory Efficient**: Only stores detailed items, not basic menu data
- **TTL-based Expiration**: Automatic cleanup prevents stale data
- **Device Isolation**: Each restaurant's cache is completely separate
- **Backward Compatible**: Existing code works without changes
- **Production Ready**: Handles failures gracefully and logs appropriately
