/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, expect, it, beforeEach, afterEach } from "vitest";
import { DetailedItemCache } from "$lib/services/DetailedItemCache.server.ts";
import type { DetailedItem } from "$lib/types";

// Simple integration test to verify cache behavior
describe("Cache Integration", () => {
  let cache: DetailedItemCache;
  const restaurantName = "test-restaurant";
  const itemId = "test-item";

  const mockItem: DetailedItem = {
    item: itemId,
    desc: "Test Item",
    detailedDesc: "Test detailed description",
    price: 999,
    modMaxSel: 0,
    modMinSel: 0,
    count: 10,
    selected: false,
    qty: 1,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  };

  beforeEach(() => {
    // Create cache with very short TTL for testing
    cache = new DetailedItemCache({
      ttlSeconds: 1, // 1 second for quick testing
      enabled: true,
      keyPrefix: "test_cache",
    });
  });

  afterEach(async () => {
    // Clean up test data
    try {
      await cache.invalidateRestaurant(restaurantName);
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  it("demonstrates basic cache functionality", async () => {
    // Initially should be empty
    const initialResult = await cache.get(restaurantName, itemId);
    expect(initialResult).toBeNull();

    // Store item in cache
    await cache.set(restaurantName, itemId, mockItem);

    // Should now return the cached item
    const cachedResult = await cache.get(restaurantName, itemId);
    expect(cachedResult).toEqual(mockItem);

    // Verify cache stats
    const stats = await cache.getStats(restaurantName);
    expect(stats.restaurantKeys).toBe(1);
    expect(stats.totalKeys).toBeGreaterThanOrEqual(1);
  });

  it("demonstrates cache invalidation", async () => {
    // Store item
    await cache.set(restaurantName, itemId, mockItem);

    // Verify it's cached
    let result = await cache.get(restaurantName, itemId);
    expect(result).toEqual(mockItem);

    // Invalidate specific item
    await cache.invalidate(restaurantName, itemId);

    // Should now be empty
    result = await cache.get(restaurantName, itemId);
    expect(result).toBeNull();
  });

  it("demonstrates restaurant-wide invalidation", async () => {
    const item1Id = "item1";
    const item2Id = "item2";

    // Store multiple items
    await cache.set(restaurantName, item1Id, { ...mockItem, item: item1Id });
    await cache.set(restaurantName, item2Id, { ...mockItem, item: item2Id });

    // Verify both are cached
    expect(await cache.get(restaurantName, item1Id)).toBeTruthy();
    expect(await cache.get(restaurantName, item2Id)).toBeTruthy();

    // Invalidate all items for restaurant
    await cache.invalidateRestaurant(restaurantName);

    // Both should now be empty
    expect(await cache.get(restaurantName, item1Id)).toBeNull();
    expect(await cache.get(restaurantName, item2Id)).toBeNull();
  });

  it("handles disabled cache gracefully", async () => {
    const disabledCache = new DetailedItemCache({ enabled: false });

    // Should always return null when disabled
    expect(await disabledCache.get(deviceId, itemId)).toBeNull();

    // Set should not throw but also not store anything
    await expect(disabledCache.set(deviceId, itemId, mockItem)).resolves.not.toThrow();

    // Still should return null
    expect(await disabledCache.get(deviceId, itemId)).toBeNull();
  });

  it("demonstrates cache key isolation between devices", async () => {
    const device1 = "device1";
    const device2 = "device2";
    const sameItemId = "same-item";

    const item1 = { ...mockItem, desc: "Item for device 1" };
    const item2 = { ...mockItem, desc: "Item for device 2" };

    // Store same item ID for different devices
    await cache.set(device1, sameItemId, item1);
    await cache.set(device2, sameItemId, item2);

    // Each device should get its own item
    const result1 = await cache.get(device1, sameItemId);
    const result2 = await cache.get(device2, sameItemId);

    expect(result1?.desc).toBe("Item for device 1");
    expect(result2?.desc).toBe("Item for device 2");

    // Clean up
    await cache.invalidateDevice(device1);
    await cache.invalidateDevice(device2);
  });
});
