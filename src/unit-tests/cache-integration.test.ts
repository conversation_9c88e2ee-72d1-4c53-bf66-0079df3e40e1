/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, expect, it, beforeEach, afterEach } from "vitest";
import { DetailedItemCache } from "$lib/services/DetailedItemCache.server.ts";
import type { DetailedItem } from "$lib/types";

// Integration test to verify cache behavior and configuration
describe("Cache Integration", () => {
  let cache: DetailedItemCache;
  const restaurantName = "test-restaurant";
  const itemId = "test-item";

  const mockItem: DetailedItem = {
    item: itemId,
    desc: "Test Item",
    detailedDesc: "Test detailed description",
    price: 999,
    modMaxSel: 0,
    modMinSel: 0,
    count: 10,
    selected: false,
    qty: 1,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  };

  beforeEach(() => {
    // Create cache with very short TTL for testing
    cache = new DetailedItemCache({
      ttlSeconds: 1, // 1 second for quick testing
      enabled: true,
      keyPrefix: "test_cache",
    });
  });

  afterEach(async () => {
    // Clean up test data
    try {
      await cache.invalidateRestaurant(restaurantName);
    } catch (error) {
      console.log("Failed to clean up test data:", error);
      // Ignore cleanup errors
    }
  });

  it("demonstrates cache configuration and key generation", async () => {
    // Test that cache is properly configured
    expect(cache).toBeDefined();

    // Test cache operations (will gracefully handle Redis unavailability)
    await expect(cache.get(restaurantName, itemId)).resolves.toBeDefined();
    await expect(cache.set(restaurantName, itemId, mockItem)).resolves.not.toThrow();
    await expect(cache.invalidate(restaurantName, itemId)).resolves.not.toThrow();
    await expect(cache.invalidateRestaurant(restaurantName)).resolves.not.toThrow();
    await expect(cache.getStats(restaurantName)).resolves.toBeDefined();
  });

  it("demonstrates cache operations with different restaurant names", async () => {
    const restaurant1 = "pizza-palace";
    const restaurant2 = "burger-barn";
    const sameItemId = "same-item";

    // Test that operations work with different restaurant names
    await expect(cache.set(restaurant1, sameItemId, mockItem)).resolves.not.toThrow();
    await expect(cache.set(restaurant2, sameItemId, mockItem)).resolves.not.toThrow();

    await expect(cache.get(restaurant1, sameItemId)).resolves.toBeDefined();
    await expect(cache.get(restaurant2, sameItemId)).resolves.toBeDefined();

    // Clean up
    await cache.invalidateRestaurant(restaurant1);
    await cache.invalidateRestaurant(restaurant2);
  });

  it("demonstrates cache stats functionality", async () => {
    // Test stats functionality
    const stats = await cache.getStats(restaurantName);
    expect(stats).toBeDefined();
    expect(typeof stats.totalKeys).toBe('number');

    const statsWithRestaurant = await cache.getStats(restaurantName);
    expect(statsWithRestaurant).toBeDefined();
    expect(typeof statsWithRestaurant.totalKeys).toBe('number');
  });

  it("handles disabled cache gracefully", async () => {
    const disabledCache = new DetailedItemCache({ enabled: false });

    // Should always return null when disabled
    expect(await disabledCache.get(restaurantName, itemId)).toBeNull();

    // Set should not throw but also not store anything
    await expect(disabledCache.set(restaurantName, itemId, mockItem)).resolves.not.toThrow();

    // Still should return null
    expect(await disabledCache.get(restaurantName, itemId)).toBeNull();
  });

  it("demonstrates cache key isolation between restaurants", async () => {
    const restaurant1 = "pizza-palace";
    const restaurant2 = "burger-barn";
    const sameItemId = "same-item";

    const item1 = { ...mockItem, desc: "Item for pizza palace" };
    const item2 = { ...mockItem, desc: "Item for burger barn" };

    // Store same item ID for different restaurants
    await cache.set(restaurant1, sameItemId, item1);
    await cache.set(restaurant2, sameItemId, item2);

    // Each restaurant should get its own item
    const result1 = await cache.get(restaurant1, sameItemId);
    const result2 = await cache.get(restaurant2, sameItemId);

    expect(result1?.desc).toBe("Item for pizza palace");
    expect(result2?.desc).toBe("Item for burger barn");

    // Clean up
    await cache.invalidateRestaurant(restaurant1);
    await cache.invalidateRestaurant(restaurant2);
  });
});
