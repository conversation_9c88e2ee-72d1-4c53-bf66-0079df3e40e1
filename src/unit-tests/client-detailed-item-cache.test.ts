/* eslint-disable @typescript-eslint/no-explicit-any */
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { ClientDetailedItemCache } from "$lib/client/cache/DetailedItemCache.ts";
import type { DetailedItem } from "$lib/types";

// Mock browser environment
vi.mock("$app/environment", () => ({
  browser: true,
}));

// Mock sessionStorage
const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
};

Object.defineProperty(global, 'sessionStorage', {
  value: mockSessionStorage,
  writable: true,
});

const createMockDetailedItem = (itemId: string): DetailedItem => ({
  item: itemId,
  desc: `Test Item ${itemId}`,
  detailedDesc: `Detailed description for ${itemId}`,
  price: 999,
  modMaxSel: 0,
  modMinSel: 0,
  count: 10,
  selected: false,
  qty: 1,
  multiModLists: false,
  isVisible: true,
  modifiers: [],
});

describe("ClientDetailedItemCache", () => {
  let cache: ClientDetailedItemCache;
  const restaurantName = "pizza-palace";
  const itemId = "test-item-456";
  const mockItem = createMockDetailedItem(itemId);

  beforeEach(() => {
    cache = new ClientDetailedItemCache();
    vi.clearAllMocks();
    mockSessionStorage.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe("constructor", () => {
    it("uses default configuration when no config provided", () => {
      const defaultCache = new ClientDetailedItemCache();
      expect(defaultCache).toBeDefined();
    });

    it("merges provided configuration with defaults", () => {
      const customCache = new ClientDetailedItemCache({
        ttlMs: 600000,
        keyPrefix: "custom_prefix",
      });
      expect(customCache).toBeDefined();
    });
  });

  describe("get", () => {
    it("returns null when cache is disabled", () => {
      const disabledCache = new ClientDetailedItemCache({ enabled: false });
      const result = disabledCache.get(restaurantName, itemId);
      expect(result).toBeNull();
    });

    it("returns null when item not found in cache", () => {
      const result = cache.get(restaurantName, itemId);
      expect(result).toBeNull();
    });

    it("returns item when found in cache and not expired", () => {
      // First set an item
      cache.set(restaurantName, itemId, mockItem);
      
      // Then get it
      const result = cache.get(restaurantName, itemId);
      expect(result).toEqual(mockItem);
    });

    it("returns null when item is expired", () => {
      // Create cache with very short TTL
      const shortTtlCache = new ClientDetailedItemCache({ ttlMs: 1 });
      
      // Set an item
      shortTtlCache.set(restaurantName, itemId, mockItem);
      
      // Wait for expiration (simulate by advancing time)
      vi.useFakeTimers();
      vi.advanceTimersByTime(2);
      
      const result = shortTtlCache.get(restaurantName, itemId);
      expect(result).toBeNull();
      
      vi.useRealTimers();
    });
  });

  describe("set", () => {
    it("does nothing when cache is disabled", () => {
      const disabledCache = new ClientDetailedItemCache({ enabled: false });
      
      disabledCache.set(restaurantName, itemId, mockItem);
      
      const result = disabledCache.get(restaurantName, itemId);
      expect(result).toBeNull();
    });

    it("stores item in cache", () => {
      cache.set(restaurantName, itemId, mockItem);
      
      const result = cache.get(restaurantName, itemId);
      expect(result).toEqual(mockItem);
    });

    it("enforces cache size limit", () => {
      const limitedCache = new ClientDetailedItemCache({ maxItems: 2 });
      
      // Add 3 items
      limitedCache.set(restaurantName, "item1", { ...mockItem, item: "item1" });
      limitedCache.set(restaurantName, "item2", { ...mockItem, item: "item2" });
      limitedCache.set(restaurantName, "item3", { ...mockItem, item: "item3" });
      
      // First item should be evicted
      expect(limitedCache.get(restaurantName, "item1")).toBeNull();
      expect(limitedCache.get(restaurantName, "item2")).toBeTruthy();
      expect(limitedCache.get(restaurantName, "item3")).toBeTruthy();
    });
  });

  describe("invalidate", () => {
    it("does nothing when cache is disabled", () => {
      const disabledCache = new ClientDetailedItemCache({ enabled: false });
      
      disabledCache.invalidate(restaurantName, itemId);
      // No error should be thrown
    });

    it("removes item from cache", () => {
      cache.set(restaurantName, itemId, mockItem);
      expect(cache.get(restaurantName, itemId)).toBeTruthy();
      
      cache.invalidate(restaurantName, itemId);
      expect(cache.get(restaurantName, itemId)).toBeNull();
    });
  });

  describe("invalidateRestaurant", () => {
    it("does nothing when cache is disabled", () => {
      const disabledCache = new ClientDetailedItemCache({ enabled: false });
      
      disabledCache.invalidateRestaurant(restaurantName);
      // No error should be thrown
    });

    it("removes all items for restaurant", () => {
      const restaurant2 = "burger-barn";
      
      // Add items for both restaurants
      cache.set(restaurantName, "item1", { ...mockItem, item: "item1" });
      cache.set(restaurantName, "item2", { ...mockItem, item: "item2" });
      cache.set(restaurant2, "item3", { ...mockItem, item: "item3" });
      
      // Invalidate one restaurant
      cache.invalidateRestaurant(restaurantName);
      
      // Items for pizza-palace should be gone
      expect(cache.get(restaurantName, "item1")).toBeNull();
      expect(cache.get(restaurantName, "item2")).toBeNull();
      
      // Items for burger-barn should remain
      expect(cache.get(restaurant2, "item3")).toBeTruthy();
    });
  });

  describe("clear", () => {
    it("removes all items from cache", () => {
      cache.set(restaurantName, "item1", { ...mockItem, item: "item1" });
      cache.set(restaurantName, "item2", { ...mockItem, item: "item2" });
      
      cache.clear();
      
      expect(cache.get(restaurantName, "item1")).toBeNull();
      expect(cache.get(restaurantName, "item2")).toBeNull();
    });
  });

  describe("getStats", () => {
    it("returns total items count", () => {
      cache.set(restaurantName, "item1", { ...mockItem, item: "item1" });
      cache.set("burger-barn", "item2", { ...mockItem, item: "item2" });
      
      const stats = cache.getStats();
      
      expect(stats.totalItems).toBe(2);
      expect(stats.memoryUsage).toBe(2);
    });

    it("returns restaurant-specific stats when restaurant name provided", () => {
      cache.set(restaurantName, "item1", { ...mockItem, item: "item1" });
      cache.set(restaurantName, "item2", { ...mockItem, item: "item2" });
      cache.set("burger-barn", "item3", { ...mockItem, item: "item3" });
      
      const stats = cache.getStats(restaurantName);
      
      expect(stats.totalItems).toBe(3);
      expect(stats.restaurantItems).toBe(2);
      expect(stats.memoryUsage).toBe(3);
    });
  });

  describe("getCacheKey", () => {
    it("generates correct cache key format", () => {
      // Test indirectly by setting and getting
      cache.set(restaurantName, itemId, mockItem);
      const result = cache.get(restaurantName, itemId);
      expect(result).toEqual(mockItem);
    });

    it("uses custom key prefix when provided", () => {
      const customCache = new ClientDetailedItemCache({ keyPrefix: "custom_prefix" });
      customCache.set(restaurantName, itemId, mockItem);
      const result = customCache.get(restaurantName, itemId);
      expect(result).toEqual(mockItem);
    });
  });

  describe("storage integration", () => {
    it("attempts to save to sessionStorage", () => {
      cache.set(restaurantName, itemId, mockItem);
      
      expect(mockSessionStorage.setItem).toHaveBeenCalled();
    });

    it("handles sessionStorage errors gracefully", () => {
      mockSessionStorage.setItem.mockImplementation(() => {
        throw new Error("Storage quota exceeded");
      });
      
      // Should not throw
      expect(() => cache.set(restaurantName, itemId, mockItem)).not.toThrow();
    });
  });
});
