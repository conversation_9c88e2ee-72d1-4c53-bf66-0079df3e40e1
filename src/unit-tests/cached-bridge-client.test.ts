import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { CachedBridgeClient } from "$lib/client/bridge/cached-bridge-client.ts";
import type { IBridgeClient } from "$lib/client/bridge/bridge-client.ts";
import type { DetailedItem } from "$lib/types";

// Mock the DetailedItemCache
vi.mock("$lib/services/DetailedItemCache.server.ts", () => ({
  DetailedItemCache: vi.fn().mockImplementation(() => ({
    get: vi.fn(),
    set: vi.fn(),
    invalidate: vi.fn(),
    invalidateDevice: vi.fn(),
    getStats: vi.fn(),
  })),
}));

// Mock logger
vi.mock("$lib/logger/logger.svelte.ts", () => ({
  logger: {
    trace: vi.fn(),
    warn: vi.fn(),
  },
}));

const createMockDetailedItem = (itemId: string): DetailedItem => ({
  item: itemId,
  desc: `Test Item ${itemId}`,
  detailedDesc: `Detailed description for ${itemId}`,
  price: 999,
  modMaxSel: 0,
  modMinSel: 0,
  count: 10,
  selected: false,
  qty: 1,
  multiModLists: false,
  isVisible: true,
  modifiers: [],
});

describe("CachedBridgeClient", () => {
  let mockBridgeClient: IBridgeClient;
  let cachedClient: CachedBridgeClient;
  let mockCache: any;
  const deviceId = "test-device-123";
  const itemId = "test-item-456";
  const mockItem = createMockDetailedItem(itemId);

  beforeEach(() => {
    // Create mock bridge client
    mockBridgeClient = {
      getMenu: vi.fn(),
      getDetailedItem: vi.fn(),
      getHealth: vi.fn(),
      getTotals: vi.fn(),
      deviceId, // Add deviceId property for testing
    } as any;

    cachedClient = new CachedBridgeClient(mockBridgeClient);
    mockCache = cachedClient.getCache();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe("constructor", () => {
    it("creates instance with default cache config", () => {
      const client = new CachedBridgeClient(mockBridgeClient);
      expect(client).toBeDefined();
    });

    it("creates instance with custom cache config", () => {
      const client = new CachedBridgeClient(mockBridgeClient, {
        ttlSeconds: 600,
        enabled: false,
      });
      expect(client).toBeDefined();
    });
  });

  describe("getMenu", () => {
    it("passes through to underlying client without caching", async () => {
      const mockMenuData = {
        departments: [],
        items: [],
        storeInfo: {} as any,
      };
      mockBridgeClient.getMenu = vi.fn().mockResolvedValue(mockMenuData);

      const result = await cachedClient.getMenu();

      expect(result).toBe(mockMenuData);
      expect(mockBridgeClient.getMenu).toHaveBeenCalledOnce();
      expect(mockCache.get).not.toHaveBeenCalled();
    });
  });

  describe("getDetailedItem", () => {
    it("returns cached item when available", async () => {
      mockCache.get.mockResolvedValue(mockItem);

      const result = await cachedClient.getDetailedItem(itemId);

      expect(result).toBe(mockItem);
      expect(mockCache.get).toHaveBeenCalledWith(deviceId, itemId);
      expect(mockBridgeClient.getDetailedItem).not.toHaveBeenCalled();
    });

    it("fetches from API when cache miss", async () => {
      mockCache.get.mockResolvedValue(null);
      mockBridgeClient.getDetailedItem = vi.fn().mockResolvedValue(mockItem);
      mockCache.set.mockResolvedValue(undefined);

      const result = await cachedClient.getDetailedItem(itemId);

      expect(result).toBe(mockItem);
      expect(mockCache.get).toHaveBeenCalledWith(deviceId, itemId);
      expect(mockBridgeClient.getDetailedItem).toHaveBeenCalledWith(itemId);
      expect(mockCache.set).toHaveBeenCalledWith(deviceId, itemId, mockItem);
    });

    it("fetches from API when cache throws error", async () => {
      mockCache.get.mockRejectedValue(new Error("Cache error"));
      mockBridgeClient.getDetailedItem = vi.fn().mockResolvedValue(mockItem);
      mockCache.set.mockResolvedValue(undefined);

      const result = await cachedClient.getDetailedItem(itemId);

      expect(result).toBe(mockItem);
      expect(mockBridgeClient.getDetailedItem).toHaveBeenCalledWith(itemId);
      expect(mockCache.set).toHaveBeenCalledWith(deviceId, itemId, mockItem);
    });

    it("continues when cache set fails", async () => {
      mockCache.get.mockResolvedValue(null);
      mockBridgeClient.getDetailedItem = vi.fn().mockResolvedValue(mockItem);
      mockCache.set.mockRejectedValue(new Error("Cache set error"));

      const result = await cachedClient.getDetailedItem(itemId);

      expect(result).toBe(mockItem);
      expect(mockBridgeClient.getDetailedItem).toHaveBeenCalledWith(itemId);
    });
  });

  describe("getHealth", () => {
    it("passes through to underlying client without caching", async () => {
      const mockHealthData = { ok: true };
      mockBridgeClient.getHealth = vi.fn().mockResolvedValue(mockHealthData);

      const result = await cachedClient.getHealth();

      expect(result).toBe(mockHealthData);
      expect(mockBridgeClient.getHealth).toHaveBeenCalledOnce();
      expect(mockCache.get).not.toHaveBeenCalled();
    });
  });

  describe("getTotals", () => {
    it("passes through to underlying client without caching", async () => {
      const mockTotalsData = {
        total: 1000,
        subTotal: 900,
        taxTotal: 100,
      } as any;
      const requestData = {
        items: [mockItem],
        promisedTime: Date.now(),
        subTotal: 900,
      };
      mockBridgeClient.getTotals = vi.fn().mockResolvedValue(mockTotalsData);

      const result = await cachedClient.getTotals(requestData);

      expect(result).toBe(mockTotalsData);
      expect(mockBridgeClient.getTotals).toHaveBeenCalledWith(requestData);
      expect(mockCache.get).not.toHaveBeenCalled();
    });
  });

  describe("invalidateItem", () => {
    it("calls cache invalidate with correct parameters", async () => {
      mockCache.invalidate.mockResolvedValue(undefined);

      await cachedClient.invalidateItem(itemId);

      expect(mockCache.invalidate).toHaveBeenCalledWith(deviceId, itemId);
    });
  });

  describe("invalidateAll", () => {
    it("calls cache invalidateDevice with correct parameters", async () => {
      mockCache.invalidateDevice.mockResolvedValue(undefined);

      await cachedClient.invalidateAll();

      expect(mockCache.invalidateDevice).toHaveBeenCalledWith(deviceId);
    });
  });

  describe("getCacheStats", () => {
    it("calls cache getStats with device ID", async () => {
      const mockStats = { totalKeys: 5, deviceKeys: 3 };
      mockCache.getStats.mockResolvedValue(mockStats);

      const result = await cachedClient.getCacheStats();

      expect(result).toBe(mockStats);
      expect(mockCache.getStats).toHaveBeenCalledWith(deviceId);
    });
  });

  describe("getUnderlyingClient", () => {
    it("returns the underlying bridge client", () => {
      const result = cachedClient.getUnderlyingClient();
      expect(result).toBe(mockBridgeClient);
    });
  });

  describe("getCache", () => {
    it("returns the cache instance", () => {
      const result = cachedClient.getCache();
      expect(result).toBe(mockCache);
    });
  });

  describe("getDeviceId", () => {
    it("extracts device ID from underlying client", async () => {
      // Test indirectly through getDetailedItem which uses getDeviceId
      mockCache.get.mockResolvedValue(mockItem);

      await cachedClient.getDetailedItem(itemId);

      expect(mockCache.get).toHaveBeenCalledWith(deviceId, itemId);
    });
  });
});
