/* eslint-disable @typescript-eslint/no-explicit-any */
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { DetailedItemCache } from "$lib/services/DetailedItemCache.server.ts";
import type { DetailedItem } from "$lib/types";

// Mock Redis - create the mock object inside the factory function
vi.mock("$lib/server/redis.server.ts", () => ({
  redis: {
    get: vi.fn(),
    setex: vi.fn(),
    del: vi.fn(),
    keys: vi.fn(),
  },
}));

// Mock logger
vi.mock("$lib/logger/logger.svelte.ts", () => ({
  logger: {
    trace: vi.fn(),
    warn: vi.fn(),
  },
}));

// Mock publicConfig
vi.mock("../../publicConfig.ts", () => ({
  publicConfig: {
    cache: {
      ttlSeconds: 300,
      enabled: true,
    },
  },
}));

const createMockDetailedItem = (itemId: string): DetailedItem => ({
  item: itemId,
  desc: `Test Item ${itemId}`,
  detailedDesc: `Detailed description for ${itemId}`,
  price: 999,
  modMaxSel: 0,
  modMinSel: 0,
  count: 10,
  selected: false,
  qty: 1,
  multiModLists: false,
  isVisible: true,
  modifiers: [],
});

describe("DetailedItemCache", () => {
  let cache: DetailedItemCache;
  let mockRedis: {
    get: ReturnType<typeof vi.fn>;
    setex: ReturnType<typeof vi.fn>;
    del: ReturnType<typeof vi.fn>;
    keys: ReturnType<typeof vi.fn>;
  };
  const restaurantName = "pizza-palace";
  const itemId = "test-item-456";
  const mockItem = createMockDetailedItem(itemId);

  beforeEach(async () => {
    // Get the mocked redis instance
    const { redis } = await import("$lib/server/redis.server.ts");
    mockRedis = redis as any;

    cache = new DetailedItemCache();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe("constructor", () => {
    it("uses default configuration when no config provided", () => {
      const defaultCache = new DetailedItemCache();
      expect(defaultCache).toBeDefined();
    });

    it("merges provided configuration with defaults", () => {
      const customCache = new DetailedItemCache({
        ttlSeconds: 600,
        keyPrefix: "custom_prefix",
      });
      expect(customCache).toBeDefined();
    });
  });

  describe("get", () => {
    it("returns null when cache is disabled", async () => {
      const disabledCache = new DetailedItemCache({ enabled: false });
      const result = await disabledCache.get(restaurantName, itemId);
      expect(result).toBeNull();
      expect(mockRedis.get).not.toHaveBeenCalled();
    });

    it("returns null when item not found in cache", async () => {
      mockRedis.get.mockResolvedValue(null);

      const result = await cache.get(restaurantName, itemId);

      expect(result).toBeNull();
      expect(mockRedis.get).toHaveBeenCalledWith("detailed_item:pizza-palace:test-item-456");
    });

    it("returns parsed item when found in cache", async () => {
      mockRedis.get.mockResolvedValue(JSON.stringify(mockItem));

      const result = await cache.get(restaurantName, itemId);

      expect(result).toEqual(mockItem);
      expect(mockRedis.get).toHaveBeenCalledWith("detailed_item:pizza-palace:test-item-456");
    });

    it("returns null and logs warning when Redis throws error", async () => {
      mockRedis.get.mockRejectedValue(new Error("Redis connection failed"));

      const result = await cache.get(restaurantName, itemId);

      expect(result).toBeNull();
    });

    it("returns null when JSON parsing fails", async () => {
      mockRedis.get.mockResolvedValue("invalid-json");

      const result = await cache.get(restaurantName, itemId);

      expect(result).toBeNull();
    });
  });

  describe("set", () => {
    it("does nothing when cache is disabled", async () => {
      const disabledCache = new DetailedItemCache({ enabled: false });

      await disabledCache.set(restaurantName, itemId, mockItem);

      expect(mockRedis.setex).not.toHaveBeenCalled();
    });

    it("stores item in cache with correct TTL", async () => {
      mockRedis.setex.mockResolvedValue("OK");

      await cache.set(restaurantName, itemId, mockItem);

      expect(mockRedis.setex).toHaveBeenCalledWith(
        "detailed_item:pizza-palace:test-item-456",
        300, // default TTL
        JSON.stringify(mockItem)
      );
    });

    it("uses custom TTL when provided", async () => {
      const customCache = new DetailedItemCache({ ttlSeconds: 600 });
      mockRedis.setex.mockResolvedValue("OK");

      await customCache.set(restaurantName, itemId, mockItem);

      expect(mockRedis.setex).toHaveBeenCalledWith(
        "detailed_item:pizza-palace:test-item-456",
        600,
        JSON.stringify(mockItem)
      );
    });

    it("does not throw when Redis fails", async () => {
      mockRedis.setex.mockRejectedValue(new Error("Redis connection failed"));

      await expect(cache.set(restaurantName, itemId, mockItem)).resolves.not.toThrow();
    });
  });

  describe("invalidate", () => {
    it("does nothing when cache is disabled", async () => {
      const disabledCache = new DetailedItemCache({ enabled: false });

      await disabledCache.invalidate(restaurantName, itemId);

      expect(mockRedis.del).not.toHaveBeenCalled();
    });

    it("deletes item from cache", async () => {
      mockRedis.del.mockResolvedValue(1);

      await cache.invalidate(restaurantName, itemId);

      expect(mockRedis.del).toHaveBeenCalledWith("detailed_item:pizza-palace:test-item-456");
    });

    it("does not throw when Redis fails", async () => {
      mockRedis.del.mockRejectedValue(new Error("Redis connection failed"));

      await expect(cache.invalidate(restaurantName, itemId)).resolves.not.toThrow();
    });
  });

  describe("invalidateRestaurant", () => {
    it("does nothing when cache is disabled", async () => {
      const disabledCache = new DetailedItemCache({ enabled: false });

      await disabledCache.invalidateRestaurant(restaurantName);

      expect(mockRedis.keys).not.toHaveBeenCalled();
    });

    it("deletes all keys for restaurant when keys exist", async () => {
      const keys = [
        "detailed_item:pizza-palace:item1",
        "detailed_item:pizza-palace:item2",
      ];
      mockRedis.keys.mockResolvedValue(keys);
      mockRedis.del.mockResolvedValue(2);

      await cache.invalidateRestaurant(restaurantName);

      expect(mockRedis.keys).toHaveBeenCalledWith("detailed_item:pizza-palace:*");
      expect(mockRedis.del).toHaveBeenCalledWith(...keys);
    });

    it("does not call del when no keys exist", async () => {
      mockRedis.keys.mockResolvedValue([]);

      await cache.invalidateRestaurant(restaurantName);

      expect(mockRedis.keys).toHaveBeenCalledWith("detailed_item:pizza-palace:*");
      expect(mockRedis.del).not.toHaveBeenCalled();
    });

    it("does not throw when Redis fails", async () => {
      mockRedis.keys.mockRejectedValue(new Error("Redis connection failed"));

      await expect(cache.invalidateRestaurant(restaurantName)).resolves.not.toThrow();
    });
  });

  describe("getStats", () => {
    it("returns total keys count", async () => {
      const allKeys = [
        "detailed_item:pizza-palace:item1",
        "detailed_item:burger-barn:item2",
      ];
      mockRedis.keys.mockResolvedValue(allKeys);

      const stats = await cache.getStats();

      expect(stats).toEqual({ totalKeys: 2 });
      expect(mockRedis.keys).toHaveBeenCalledWith("detailed_item:*");
    });

    it("returns restaurant-specific stats when restaurant name provided", async () => {
      const allKeys = [
        "detailed_item:pizza-palace:item1",
        "detailed_item:burger-barn:item2",
      ];
      const restaurantKeys = ["detailed_item:pizza-palace:item1"];

      mockRedis.keys
        .mockResolvedValueOnce(allKeys)
        .mockResolvedValueOnce(restaurantKeys);

      const stats = await cache.getStats("pizza-palace");

      expect(stats).toEqual({ totalKeys: 2, restaurantKeys: 1 });
      expect(mockRedis.keys).toHaveBeenCalledWith("detailed_item:*");
      expect(mockRedis.keys).toHaveBeenCalledWith("detailed_item:pizza-palace:*");
    });

    it("returns zero stats when Redis fails", async () => {
      mockRedis.keys.mockRejectedValue(new Error("Redis connection failed"));

      const stats = await cache.getStats();

      expect(stats).toEqual({ totalKeys: 0 });
    });
  });

  describe("getCacheKey", () => {
    it("generates correct cache key format", async () => {
      // Test the key format indirectly through get method
      mockRedis.get.mockResolvedValue(null);

      await cache.get(restaurantName, itemId);

      expect(mockRedis.get).toHaveBeenCalledWith("detailed_item:pizza-palace:test-item-456");
    });

    it("uses custom key prefix when provided", async () => {
      const customCache = new DetailedItemCache({ keyPrefix: "custom_prefix" });
      mockRedis.get.mockResolvedValue(null);

      await customCache.get(restaurantName, itemId);

      expect(mockRedis.get).toHaveBeenCalledWith("custom_prefix:pizza-palace:test-item-456");
    });
  });
});
