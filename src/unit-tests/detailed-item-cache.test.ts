/* eslint-disable @typescript-eslint/no-explicit-any */
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { DetailedItemCache } from "$lib/services/DetailedItemCache.server.ts";
import type { DetailedItem } from "$lib/types";

// Mock Redis - create the mock object inside the factory function
vi.mock("$lib/server/redis.server.ts", () => ({
  redis: {
    get: vi.fn(),
    setex: vi.fn(),
    del: vi.fn(),
    keys: vi.fn(),
  },
}));

// Mock logger
vi.mock("$lib/logger/logger.svelte.ts", () => ({
  logger: {
    trace: vi.fn(),
    warn: vi.fn(),
  },
}));

// Mock publicConfig
vi.mock("../../publicConfig.ts", () => ({
  publicConfig: {
    cache: {
      ttlSeconds: 300,
      enabled: true,
    },
  },
}));

const createMockDetailedItem = (itemId: string): DetailedItem => ({
  item: itemId,
  desc: `Test Item ${itemId}`,
  detailedDesc: `Detailed description for ${itemId}`,
  price: 999,
  modMaxSel: 0,
  modMinSel: 0,
  count: 10,
  selected: false,
  qty: 1,
  multiModLists: false,
  isVisible: true,
  modifiers: [],
});

describe("DetailedItemCache", () => {
  let cache: DetailedItemCache;
  let mockRedis: {
    get: ReturnType<typeof vi.fn>;
    setex: ReturnType<typeof vi.fn>;
    del: ReturnType<typeof vi.fn>;
    keys: ReturnType<typeof vi.fn>;
  };
  const deviceId = "test-device-123";
  const itemId = "test-item-456";
  const mockItem = createMockDetailedItem(itemId);

  beforeEach(async () => {
    // Get the mocked redis instance
    const { redis } = await import("$lib/server/redis.server.ts");
    mockRedis = redis as any;

    cache = new DetailedItemCache();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe("constructor", () => {
    it("uses default configuration when no config provided", () => {
      const defaultCache = new DetailedItemCache();
      expect(defaultCache).toBeDefined();
    });

    it("merges provided configuration with defaults", () => {
      const customCache = new DetailedItemCache({
        ttlSeconds: 600,
        keyPrefix: "custom_prefix",
      });
      expect(customCache).toBeDefined();
    });
  });

  describe("get", () => {
    it("returns null when cache is disabled", async () => {
      const disabledCache = new DetailedItemCache({ enabled: false });
      const result = await disabledCache.get(deviceId, itemId);
      expect(result).toBeNull();
      expect(mockRedis.get).not.toHaveBeenCalled();
    });

    it("returns null when item not found in cache", async () => {
      mockRedis.get.mockResolvedValue(null);

      const result = await cache.get(deviceId, itemId);

      expect(result).toBeNull();
      expect(mockRedis.get).toHaveBeenCalledWith("detailed_item:test-device-123:test-item-456");
    });

    it("returns parsed item when found in cache", async () => {
      mockRedis.get.mockResolvedValue(JSON.stringify(mockItem));

      const result = await cache.get(deviceId, itemId);

      expect(result).toEqual(mockItem);
      expect(mockRedis.get).toHaveBeenCalledWith("detailed_item:test-device-123:test-item-456");
    });

    it("returns null and logs warning when Redis throws error", async () => {
      mockRedis.get.mockRejectedValue(new Error("Redis connection failed"));

      const result = await cache.get(deviceId, itemId);

      expect(result).toBeNull();
    });

    it("returns null when JSON parsing fails", async () => {
      mockRedis.get.mockResolvedValue("invalid-json");

      const result = await cache.get(deviceId, itemId);

      expect(result).toBeNull();
    });
  });

  describe("set", () => {
    it("does nothing when cache is disabled", async () => {
      const disabledCache = new DetailedItemCache({ enabled: false });

      await disabledCache.set(deviceId, itemId, mockItem);

      expect(mockRedis.setex).not.toHaveBeenCalled();
    });

    it("stores item in cache with correct TTL", async () => {
      mockRedis.setex.mockResolvedValue("OK");

      await cache.set(deviceId, itemId, mockItem);

      expect(mockRedis.setex).toHaveBeenCalledWith(
        "detailed_item:test-device-123:test-item-456",
        300, // default TTL
        JSON.stringify(mockItem)
      );
    });

    it("uses custom TTL when provided", async () => {
      const customCache = new DetailedItemCache({ ttlSeconds: 600 });
      mockRedis.setex.mockResolvedValue("OK");

      await customCache.set(deviceId, itemId, mockItem);

      expect(mockRedis.setex).toHaveBeenCalledWith(
        "detailed_item:test-device-123:test-item-456",
        600,
        JSON.stringify(mockItem)
      );
    });

    it("does not throw when Redis fails", async () => {
      mockRedis.setex.mockRejectedValue(new Error("Redis connection failed"));

      await expect(cache.set(deviceId, itemId, mockItem)).resolves.not.toThrow();
    });
  });

  describe("invalidate", () => {
    it("does nothing when cache is disabled", async () => {
      const disabledCache = new DetailedItemCache({ enabled: false });

      await disabledCache.invalidate(deviceId, itemId);

      expect(mockRedis.del).not.toHaveBeenCalled();
    });

    it("deletes item from cache", async () => {
      mockRedis.del.mockResolvedValue(1);

      await cache.invalidate(deviceId, itemId);

      expect(mockRedis.del).toHaveBeenCalledWith("detailed_item:test-device-123:test-item-456");
    });

    it("does not throw when Redis fails", async () => {
      mockRedis.del.mockRejectedValue(new Error("Redis connection failed"));

      await expect(cache.invalidate(deviceId, itemId)).resolves.not.toThrow();
    });
  });

  describe("invalidateDevice", () => {
    it("does nothing when cache is disabled", async () => {
      const disabledCache = new DetailedItemCache({ enabled: false });

      await disabledCache.invalidateDevice(deviceId);

      expect(mockRedis.keys).not.toHaveBeenCalled();
    });

    it("deletes all keys for device when keys exist", async () => {
      const keys = [
        "detailed_item:test-device-123:item1",
        "detailed_item:test-device-123:item2",
      ];
      mockRedis.keys.mockResolvedValue(keys);
      mockRedis.del.mockResolvedValue(2);

      await cache.invalidateDevice(deviceId);

      expect(mockRedis.keys).toHaveBeenCalledWith("detailed_item:test-device-123:*");
      expect(mockRedis.del).toHaveBeenCalledWith(...keys);
    });

    it("does not call del when no keys exist", async () => {
      mockRedis.keys.mockResolvedValue([]);

      await cache.invalidateDevice(deviceId);

      expect(mockRedis.keys).toHaveBeenCalledWith("detailed_item:test-device-123:*");
      expect(mockRedis.del).not.toHaveBeenCalled();
    });

    it("does not throw when Redis fails", async () => {
      mockRedis.keys.mockRejectedValue(new Error("Redis connection failed"));

      await expect(cache.invalidateDevice(deviceId)).resolves.not.toThrow();
    });
  });

  describe("getStats", () => {
    it("returns total keys count", async () => {
      const allKeys = [
        "detailed_item:device1:item1",
        "detailed_item:device2:item2",
      ];
      mockRedis.keys.mockResolvedValue(allKeys);

      const stats = await cache.getStats();

      expect(stats).toEqual({ totalKeys: 2 });
      expect(mockRedis.keys).toHaveBeenCalledWith("detailed_item:*");
    });

    it("returns device-specific stats when deviceId provided", async () => {
      const allKeys = [
        "detailed_item:device1:item1",
        "detailed_item:device2:item2",
      ];
      const deviceKeys = ["detailed_item:device1:item1"];

      mockRedis.keys
        .mockResolvedValueOnce(allKeys)
        .mockResolvedValueOnce(deviceKeys);

      const stats = await cache.getStats("device1");

      expect(stats).toEqual({ totalKeys: 2, deviceKeys: 1 });
      expect(mockRedis.keys).toHaveBeenCalledWith("detailed_item:*");
      expect(mockRedis.keys).toHaveBeenCalledWith("detailed_item:device1:*");
    });

    it("returns zero stats when Redis fails", async () => {
      mockRedis.keys.mockRejectedValue(new Error("Redis connection failed"));

      const stats = await cache.getStats();

      expect(stats).toEqual({ totalKeys: 0 });
    });
  });

  describe("getCacheKey", () => {
    it("generates correct cache key format", async () => {
      // Test the key format indirectly through get method
      mockRedis.get.mockResolvedValue(null);

      await cache.get(deviceId, itemId);

      expect(mockRedis.get).toHaveBeenCalledWith("detailed_item:test-device-123:test-item-456");
    });

    it("uses custom key prefix when provided", async () => {
      const customCache = new DetailedItemCache({ keyPrefix: "custom_prefix" });
      mockRedis.get.mockResolvedValue(null);

      await customCache.get(deviceId, itemId);

      expect(mockRedis.get).toHaveBeenCalledWith("custom_prefix:test-device-123:test-item-456");
    });
  });
});
