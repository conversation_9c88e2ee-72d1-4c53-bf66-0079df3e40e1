import { json, type Request<PERSON><PERSON><PERSON> } from "@sveltejs/kit";
import { getApiBaseUrl } from "$lib/helpers/get-dynamic-url.ts";
import { detailedItemCache } from "$lib/services/DetailedItemCache.server.ts";
import { logger } from "$lib/logger/logger.svelte.ts";
import type { DetailedItem } from "$lib/types";

export const GET: RequestHandler = async (event) => {
  const { locals, params } = event;
  const { bridgeApiToken } = locals;
  const { itemId, deviceId } = params;

  // Try to get from cache first
  try {
    const cachedItem = await detailedItemCache.get(deviceId, itemId);
    if (cachedItem) {
      logger.trace({ deviceId, itemId }, "Serving detailed item from server-side cache");
      return json(cachedItem);
    }
  } catch (error) {
    logger.warn(
      { deviceId, itemId, error },
      "Server-side cache lookup failed, proceeding with API call"
    );
  }

  // Cache miss - fetch from bridge API
  const bridgeEndpointAddress = `${getApiBaseUrl(deviceId)}/item/online/${itemId}`;

  const response = await fetch(bridgeEndpointAddress, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${bridgeApiToken}`,
    },
  });

  if (!response.ok) {
    // Handle non-OK responses properly
    return json(
      {
        error: `Failed to fetch item: ${response.status} ${response.statusText}`,
        itemId: params.itemId,
      },
      { status: response.status },
    );
  }

  const body = await response.json() as DetailedItem;

  // Store in cache for future requests (fire and forget)
  detailedItemCache.set(deviceId, itemId, body).catch((error) => {
    logger.warn(
      { deviceId, itemId, error },
      "Failed to cache detailed item after API fetch"
    );
  });

  return json(body);
};
