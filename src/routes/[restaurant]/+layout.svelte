<script lang="ts">
  import {
    BridgeClient,
    setBridgeClient,
  } from "$lib/client/bridge/bridge-client.js";
  import { MockBridgeClient } from "$lib/client/bridge/bridge-client.mock.js";
  import { CachedBridgeClient } from "$lib/client/bridge/cached-bridge-client.js";
  import { setCartState } from "$lib/services/Cart.svelte.js";
  import { setStoreInfoState } from "$lib/services/StoreInfo.svelte";
  import { initHeaderState } from "$lib/services/TopHeader.svelte.js";
  import Header from "$lib/components/Header.svelte";
  import { setHealthCheckService } from "$lib/services/HealthCheckService.svelte.js";
  import { publicConfig } from "../../publicConfig.js";

  const { data, children } = $props();
  const { restaurant, restaurantInfo } = data;

  // Create the base bridge client
  const baseBridgeClient =
    publicConfig.env === "dev"
      ? new MockBridgeClient(fetch, restaurantInfo.device_id)
      : new BridgeClient(fetch, restaurantInfo.device_id);

  // Wrap with caching for detailed items using configuration
  const bridgeClient = new CachedBridgeClient(baseBridgeClient, restaurant, {
    ttlSeconds: publicConfig.cache.ttlSeconds,
    enabled: publicConfig.cache.enabled,
  });

  setStoreInfoState(restaurantInfo.storeInfo);
  initHeaderState();
  setCartState();

  setBridgeClient(bridgeClient);
  const healthCheckService = setHealthCheckService(bridgeClient);
  healthCheckService.startPolling();
</script>

<Header
  bannerImageUrl={data.bannerImageExists ? data.bannerImageUrl : undefined}
/>

{@render children()}
