import { logger } from "$lib/logger/logger.svelte.js";
import { getRestaurantInfo } from "$lib/server/get-restaurant-info.ts";
import { error, isHttpError } from "@sveltejs/kit";
import fs from "node:fs/promises";
import path from "node:path";

export const load = async (event) => {
  const { restaurant } = event.params;

  try {
    const restaurantInfo = await getRestaurantInfo(event.fetch, restaurant);

    if (restaurantInfo.items.length === 0) {
      logger.error({ restaurant, restaurantInfo }, "Restaurant info is empty");
      error(503, {
        message:
          "Our servers are temporarily unable to process your request. Please try again later.",
      });
    }

    // TODO This will be dynamic per merchant in the future
    const BANNER_IMAGE_FILENAME = "merchant_banner.png";
    const imageFilePath = path.join(
      process.cwd(),
      "static",
      BANNER_IMAGE_FILENAME,
    );

    let bannerImageExists = false;
    try {
      await fs.access(imageFilePath);
      bannerImageExists = true;
    } catch {
      bannerImageExists = false;
    }

    return {
      restaurant, // Pass the restaurant name to the client
      restaurantInfo,
      bannerImageExists,
      bannerImageUrl: `/${BANNER_IMAGE_FILENAME}`,
    };
  } catch (err) {
    logger.error(err, "Error loading restaurant info");
    if (isHttpError(err)) {
      error(err.status, err.body);
    } else {
      error(500, err instanceof Error ? err.message : "Unknown error");
    }
  }
};
