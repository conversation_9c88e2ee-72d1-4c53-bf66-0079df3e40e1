import { env } from "$env/dynamic/public";

const {
  PUBLIC_ENV,
  PUBLIC_LOG_LEVEL,
  PUBLIC_CACHE_ENABLED,
  PUBLIC_CACHE_TTL_SECONDS
} = env;

export const publicConfig = {
  /**
   * dev | staging | production
   */
  env: "staging",
  logLevel: PUBLIC_LOG_LEVEL || "info",

  /**
   * Cache configuration for detailed items
   */
  cache: {
    /** Whether detailed item caching is enabled */
    enabled: PUBLIC_CACHE_ENABLED !== "false", // Default to true unless explicitly disabled
    /** TTL in seconds for cached detailed items */
    ttlSeconds: parseInt(PUBLIC_CACHE_TTL_SECONDS || "300", 10), // Default 5 minutes
  },
};
