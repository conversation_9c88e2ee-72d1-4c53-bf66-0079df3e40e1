import type { DetailedItem } from "$lib/types";
import { browser } from "$app/environment";

/**
 * Configuration for the client-side detailed item cache
 */
export interface ClientCacheConfig {
  /** TTL in milliseconds for cached items (default: 300000 = 5 minutes) */
  ttlMs: number;
  /** Whether caching is enabled (default: true) */
  enabled: boolean;
  /** Key prefix for storage keys (default: "detailed_item") */
  keyPrefix: string;
  /** Maximum number of items to cache (default: 100) */
  maxItems: number;
}

/**
 * Default cache configuration
 */
const DEFAULT_CONFIG: ClientCacheConfig = {
  ttlMs: 300000, // 5 minutes
  enabled: true,
  keyPrefix: "detailed_item",
  maxItems: 100,
};

/**
 * Cached item with timestamp
 */
interface CachedItem {
  data: DetailedItem;
  timestamp: number;
  restaurantName: string;
  itemId: string;
}

/**
 * Client-side cache for detailed items using browser storage
 * 
 * This cache works entirely in the browser using sessionStorage for temporary caching.
 * It provides fast access to recently viewed detailed items without server round-trips.
 */
export class ClientDetailedItemCache {
  private config: ClientCacheConfig;
  private memoryCache: Map<string, CachedItem> = new Map();

  constructor(config: Partial<ClientCacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    // Load existing cache from sessionStorage if available
    if (browser) {
      this.loadFromStorage();
    }
  }

  /**
   * Generate a cache key for a detailed item
   */
  private getCacheKey(restaurantName: string, itemId: string): string {
    return `${this.config.keyPrefix}:${restaurantName}:${itemId}`;
  }

  /**
   * Check if an item is expired
   */
  private isExpired(item: CachedItem): boolean {
    return Date.now() - item.timestamp > this.config.ttlMs;
  }

  /**
   * Clean up expired items from memory cache
   */
  private cleanupExpired(): void {
    const now = Date.now();
    for (const [key, item] of this.memoryCache.entries()) {
      if (now - item.timestamp > this.config.ttlMs) {
        this.memoryCache.delete(key);
      }
    }
  }

  /**
   * Enforce cache size limit
   */
  private enforceSizeLimit(): void {
    if (this.memoryCache.size <= this.config.maxItems) {
      return;
    }

    // Remove oldest items first
    const entries = Array.from(this.memoryCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    const itemsToRemove = entries.slice(0, this.memoryCache.size - this.config.maxItems);
    for (const [key] of itemsToRemove) {
      this.memoryCache.delete(key);
    }
  }

  /**
   * Load cache from sessionStorage
   */
  private loadFromStorage(): void {
    if (!browser) return;

    try {
      const stored = sessionStorage.getItem(`${this.config.keyPrefix}_cache`);
      if (stored) {
        const data = JSON.parse(stored) as Array<[string, CachedItem]>;
        this.memoryCache = new Map(data);
        this.cleanupExpired();
      }
    } catch (error) {
      console.warn('Failed to load cache from storage:', error);
    }
  }

  /**
   * Save cache to sessionStorage
   */
  private saveToStorage(): void {
    if (!browser) return;

    try {
      const data = Array.from(this.memoryCache.entries());
      sessionStorage.setItem(`${this.config.keyPrefix}_cache`, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save cache to storage:', error);
    }
  }

  /**
   * Get a detailed item from cache
   * @param restaurantName - The restaurant name from the URL
   * @param itemId - The item ID
   * @returns The cached detailed item or null if not found/expired
   */
  get(restaurantName: string, itemId: string): DetailedItem | null {
    if (!this.config.enabled) {
      return null;
    }

    const key = this.getCacheKey(restaurantName, itemId);
    const cached = this.memoryCache.get(key);

    if (!cached) {
      return null;
    }

    if (this.isExpired(cached)) {
      this.memoryCache.delete(key);
      this.saveToStorage();
      return null;
    }

    return cached.data;
  }

  /**
   * Store a detailed item in cache
   * @param restaurantName - The restaurant name from the URL
   * @param itemId - The item ID
   * @param detailedItem - The detailed item data to cache
   */
  set(restaurantName: string, itemId: string, detailedItem: DetailedItem): void {
    if (!this.config.enabled) {
      return;
    }

    const key = this.getCacheKey(restaurantName, itemId);
    const cached: CachedItem = {
      data: detailedItem,
      timestamp: Date.now(),
      restaurantName,
      itemId,
    };

    this.memoryCache.set(key, cached);
    this.enforceSizeLimit();
    this.saveToStorage();
  }

  /**
   * Remove a specific item from cache
   * @param restaurantName - The restaurant name from the URL
   * @param itemId - The item ID
   */
  invalidate(restaurantName: string, itemId: string): void {
    if (!this.config.enabled) {
      return;
    }

    const key = this.getCacheKey(restaurantName, itemId);
    this.memoryCache.delete(key);
    this.saveToStorage();
  }

  /**
   * Clear all cached items for a specific restaurant
   * @param restaurantName - The restaurant name from the URL
   */
  invalidateRestaurant(restaurantName: string): void {
    if (!this.config.enabled) {
      return;
    }

    const keysToDelete: string[] = [];
    for (const [key, item] of this.memoryCache.entries()) {
      if (item.restaurantName === restaurantName) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) {
      this.memoryCache.delete(key);
    }

    this.saveToStorage();
  }

  /**
   * Clear all cached items
   */
  clear(): void {
    this.memoryCache.clear();
    if (browser) {
      try {
        sessionStorage.removeItem(`${this.config.keyPrefix}_cache`);
      } catch (error) {
        console.warn('Failed to clear cache from storage:', error);
      }
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  getStats(restaurantName?: string): {
    totalItems: number;
    restaurantItems?: number;
    memoryUsage: number;
  } {
    this.cleanupExpired();

    const stats = {
      totalItems: this.memoryCache.size,
      memoryUsage: this.memoryCache.size,
    };

    if (restaurantName) {
      let restaurantItems = 0;
      for (const item of this.memoryCache.values()) {
        if (item.restaurantName === restaurantName) {
          restaurantItems++;
        }
      }
      return {
        ...stats,
        restaurantItems,
      };
    }

    return stats;
  }
}
