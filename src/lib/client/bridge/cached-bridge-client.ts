import type { DetailedItem } from "$lib/types";
import type { IBridgeClient } from "./bridge-client.ts";
import { ClientDetailedItemCache, type ClientCacheConfig } from "$lib/client/cache/DetailedItemCache.ts";

/**
 * Cached wrapper for the bridge client that provides transparent caching
 * for detailed items while maintaining the same interface as IBridgeClient.
 * 
 * This wrapper intercepts getDetailedItem calls and:
 * 1. Checks cache first
 * 2. Falls back to the underlying bridge client if cache miss
 * 3. Stores the result in cache for future requests
 * 4. Gracefully handles cache failures without breaking functionality
 */
export class CachedBridgeClient implements IBridgeClient {
  private bridgeClient: IBridgeClient;
  private cache: ClientDetailedItemCache;
  private restaurantName: string;

  constructor(
    bridgeClient: IBridgeClient,
    restaurantName: string,
    cacheConfig?: Partial<ClientCacheConfig>
  ) {
    this.bridgeClient = bridgeClient;
    this.restaurantName = restaurantName;
    this.cache = new ClientDetailedItemCache(cacheConfig);
  }

  /**
   * Get menu data - passes through to underlying client (no caching)
   */
  async getMenu() {
    return this.bridgeClient.getMenu();
  }

  /**
   * Get detailed item with caching
   * @param itemId - The item ID to fetch
   * @returns The detailed item data
   */
  async getDetailedItem(itemId: string): Promise<DetailedItem> {
    // Try to get from cache first
    try {
      const cachedItem = this.cache.get(this.restaurantName, itemId);
      if (cachedItem) {
        console.debug(`[Cache] Serving detailed item from cache: ${this.restaurantName}/${itemId}`);
        return cachedItem;
      }
    } catch (error) {
      console.warn(`[Cache] Cache lookup failed: ${this.restaurantName}/${itemId}`, error);
    }

    // Cache miss or error - fetch from API
    console.debug(`[Cache] Cache miss, fetching detailed item from API: ${this.restaurantName}/${itemId}`);
    const detailedItem = await this.bridgeClient.getDetailedItem(itemId);

    // Store in cache for next time
    try {
      this.cache.set(this.restaurantName, itemId, detailedItem);
      console.debug(`[Cache] Cached detailed item: ${this.restaurantName}/${itemId}`);
    } catch (error) {
      console.warn(`[Cache] Failed to cache detailed item: ${this.restaurantName}/${itemId}`, error);
    }

    return detailedItem;
  }

  /**
   * Get health status - passes through to underlying client (no caching)
   */
  async getHealth() {
    return this.bridgeClient.getHealth();
  }

  /**
   * Get totals - passes through to underlying client (no caching)
   */
  async getTotals(requestData: {
    items: DetailedItem[];
    promisedTime: number;
    subTotal: number;
  }) {
    return this.bridgeClient.getTotals(requestData);
  }



  /**
   * Invalidate cache for a specific item
   * Useful when you know an item has been updated
   */
  invalidateItem(itemId: string): void {
    this.cache.invalidate(this.restaurantName, itemId);
  }

  /**
   * Invalidate all cached items for this restaurant
   * Useful for cache warming or when menu changes
   */
  invalidateAll(): void {
    this.cache.invalidateRestaurant(this.restaurantName);
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats() {
    return this.cache.getStats(this.restaurantName);
  }

  /**
   * Get the underlying bridge client (for advanced use cases)
   */
  getUnderlyingClient(): IBridgeClient {
    return this.bridgeClient;
  }

  /**
   * Get the cache instance (for advanced use cases)
   */
  getCache(): ClientDetailedItemCache {
    return this.cache;
  }
}
