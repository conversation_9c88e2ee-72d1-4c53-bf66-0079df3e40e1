import type { DetailedItem } from "$lib/types";
import type { IBridgeClient } from "./bridge-client.ts";
import { DetailedItemCache, type DetailedItemCacheConfig } from "$lib/services/DetailedItemCache.server.ts";
import { logger } from "$lib/logger/logger.svelte.ts";

/**
 * Cached wrapper for the bridge client that provides transparent caching
 * for detailed items while maintaining the same interface as IBridgeClient.
 * 
 * This wrapper intercepts getDetailedItem calls and:
 * 1. Checks cache first
 * 2. Falls back to the underlying bridge client if cache miss
 * 3. Stores the result in cache for future requests
 * 4. Gracefully handles cache failures without breaking functionality
 */
export class CachedBridgeClient implements IBridgeClient {
  private bridgeClient: IBridgeClient;
  private cache: DetailedItemCache;

  constructor(
    bridgeClient: IBridgeClient,
    cacheConfig?: Partial<DetailedItemCacheConfig>
  ) {
    this.bridgeClient = bridgeClient;
    this.cache = new DetailedItemCache(cacheConfig);
  }

  /**
   * Get menu data - passes through to underlying client (no caching)
   */
  async getMenu() {
    return this.bridgeClient.getMenu();
  }

  /**
   * Get detailed item with caching
   * @param itemId - The item ID to fetch
   * @returns The detailed item data
   */
  async getDetailedItem(itemId: string): Promise<DetailedItem> {
    const deviceId = this.getDeviceId();
    
    // Try to get from cache first
    try {
      const cachedItem = await this.cache.get(deviceId, itemId);
      if (cachedItem) {
        logger.trace({ deviceId, itemId }, "Serving detailed item from cache");
        return cachedItem;
      }
    } catch (error) {
      logger.warn(
        { deviceId, itemId, error },
        "Cache lookup failed, proceeding with API call"
      );
    }

    // Cache miss or error - fetch from API
    logger.trace({ deviceId, itemId }, "Cache miss, fetching detailed item from API");
    const detailedItem = await this.bridgeClient.getDetailedItem(itemId);

    // Store in cache for next time (fire and forget)
    this.cache.set(deviceId, itemId, detailedItem).catch((error) => {
      logger.warn(
        { deviceId, itemId, error },
        "Failed to cache detailed item after API fetch"
      );
    });

    return detailedItem;
  }

  /**
   * Get health status - passes through to underlying client (no caching)
   */
  async getHealth() {
    return this.bridgeClient.getHealth();
  }

  /**
   * Get totals - passes through to underlying client (no caching)
   */
  async getTotals(requestData: {
    items: DetailedItem[];
    promisedTime: number;
    subTotal: number;
  }) {
    return this.bridgeClient.getTotals(requestData);
  }

  /**
   * Get the device ID from the underlying bridge client
   * This assumes the bridge client has a deviceId property
   */
  private getDeviceId(): string {
    // Type assertion to access deviceId - this is safe because both
    // BridgeClient and MockBridgeClient have this property
    return (this.bridgeClient as any).deviceId;
  }

  /**
   * Invalidate cache for a specific item
   * Useful when you know an item has been updated
   */
  async invalidateItem(itemId: string): Promise<void> {
    const deviceId = this.getDeviceId();
    await this.cache.invalidate(deviceId, itemId);
  }

  /**
   * Invalidate all cached items for this device
   * Useful for cache warming or when menu changes
   */
  async invalidateAll(): Promise<void> {
    const deviceId = this.getDeviceId();
    await this.cache.invalidateDevice(deviceId);
  }

  /**
   * Get cache statistics for monitoring
   */
  async getCacheStats() {
    const deviceId = this.getDeviceId();
    return this.cache.getStats(deviceId);
  }

  /**
   * Get the underlying bridge client (for advanced use cases)
   */
  getUnderlyingClient(): IBridgeClient {
    return this.bridgeClient;
  }

  /**
   * Get the cache instance (for advanced use cases)
   */
  getCache(): DetailedItemCache {
    return this.cache;
  }
}
