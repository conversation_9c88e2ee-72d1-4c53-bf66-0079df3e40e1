import type { DetailedItem } from "$lib/types";
import type { IBridgeClient } from "./bridge-client.ts";
import { ClientDetailedItemCache, type ClientCacheConfig } from "$lib/client/cache/DetailedItemCache.ts";

/**
 * Cached wrapper for the bridge client that provides transparent caching
 * for detailed items while maintaining the same interface as IBridgeClient.
 *
 * This wrapper intercepts getDetailedItem calls and:
 * 1. Checks cache first
 * 2. Falls back to the underlying bridge client if cache miss
 * 3. Stores the result in cache for future requests
 * 4. Gracefully handles cache failures without breaking functionality
 *
 * Uses a Proxy to automatically forward all other methods to the underlying client.
 */
export class CachedBridgeClient implements IBridgeClient {
  private bridgeClient: IBridgeClient;
  private cache: ClientDetailedItemCache;
  private restaurantName: string;

  constructor(
    bridgeClient: IBridgeClient,
    restaurantName: string,
    cacheConfig?: Partial<ClientCacheConfig>
  ) {
    this.bridgeClient = bridgeClient;
    this.restaurantName = restaurantName;
    this.cache = new ClientDetailedItemCache(cacheConfig);

    // Return a Proxy that automatically forwards all methods except the ones we override
    return new Proxy(this, {
      get(target, prop, receiver) {
        // If it's a method we've explicitly defined, use our implementation
        if (prop in target) {
          return Reflect.get(target, prop, receiver);
        }

        // Otherwise, forward to the underlying bridge client
        const bridgeValue = Reflect.get(target.bridgeClient, prop);

        // If it's a function, bind it to the bridge client
        if (typeof bridgeValue === 'function') {
          return bridgeValue.bind(target.bridgeClient);
        }

        return bridgeValue;
      }
    }) as CachedBridgeClient & IBridgeClient;
  }

  /**
   * Get detailed item with caching
   * @param itemId - The item ID to fetch
   * @returns The detailed item data
   */
  async getDetailedItem(itemId: string): Promise<DetailedItem> {
    // Try to get from cache first
    try {
      const cachedItem = this.cache.get(this.restaurantName, itemId);
      if (cachedItem) {
        console.debug(`[Cache] Serving detailed item from cache: ${this.restaurantName}/${itemId}`);
        return cachedItem;
      }
    } catch (error) {
      console.warn(`[Cache] Cache lookup failed: ${this.restaurantName}/${itemId}`, error);
    }

    // Cache miss or error - fetch from API
    console.debug(`[Cache] Cache miss, fetching detailed item from API: ${this.restaurantName}/${itemId}`);
    const detailedItem = await this.bridgeClient.getDetailedItem(itemId);

    // Store in cache for next time
    try {
      this.cache.set(this.restaurantName, itemId, detailedItem);
      console.debug(`[Cache] Cached detailed item: ${this.restaurantName}/${itemId}`);
    } catch (error) {
      console.warn(`[Cache] Failed to cache detailed item: ${this.restaurantName}/${itemId}`, error);
    }

    return detailedItem;
  }



  /**
   * Invalidate cache for a specific item
   * Useful when you know an item has been updated
   */
  invalidateItem(itemId: string): void {
    this.cache.invalidate(this.restaurantName, itemId);
  }

  /**
   * Invalidate all cached items for this restaurant
   * Useful for cache warming or when menu changes
   */
  invalidateAll(): void {
    this.cache.invalidateRestaurant(this.restaurantName);
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats() {
    return this.cache.getStats(this.restaurantName);
  }

  /**
   * Get the underlying bridge client (for advanced use cases)
   */
  getUnderlyingClient(): IBridgeClient {
    return this.bridgeClient;
  }

  /**
   * Get the cache instance (for advanced use cases)
   */
  getCache(): ClientDetailedItemCache {
    return this.cache;
  }
}
