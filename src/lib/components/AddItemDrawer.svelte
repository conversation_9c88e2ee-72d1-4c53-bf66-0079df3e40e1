<script lang="ts">
  import Drawer from "$lib/components/Drawer.svelte";
  import type { Snippet } from "svelte";
  import { type BasicItem } from "$lib/types";

  let basicItem = $state<BasicItem | undefined>(undefined);
  let addItemDrawer: ReturnType<typeof Drawer> | undefined = $state(undefined);
  let children = $state<Snippet>();

  export const open = (props: { children: Snippet }) => {
    children = props.children;
    addItemDrawer?.open();
  };

  export const close = () => {
    addItemDrawer?.close();
  };
</script>

<Drawer
  bind:this={addItemDrawer}
  title={basicItem?.desc ?? ""}
  onClose={() => addItemDrawer?.close()}
>
  {#if children}
    {@render children()}
  {/if}
</Drawer>
