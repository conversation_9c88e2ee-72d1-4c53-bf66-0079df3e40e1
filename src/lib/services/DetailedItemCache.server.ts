import type { DetailedItem } from "$lib/types";
import { redis } from "$lib/server/redis.server.ts";
import { logger } from "$lib/logger/logger.svelte.ts";
import { publicConfig } from "../../publicConfig.ts";

/**
 * Configuration for the detailed item cache
 */
export interface DetailedItemCacheConfig {
  /** TTL in seconds for cached items (default: 300 = 5 minutes) */
  ttlSeconds: number;
  /** Whether caching is enabled (default: true) */
  enabled: boolean;
  /** Key prefix for Redis keys (default: "detailed_item") */
  keyPrefix: string;
}

/**
 * Default cache configuration
 */
const DEFAULT_CONFIG: DetailedItemCacheConfig = {
  ttlSeconds: publicConfig.cache.ttlSeconds,
  enabled: publicConfig.cache.enabled,
  keyPrefix: "detailed_item",
};

/**
 * Simple and intuitive cache service for detailed items with short TTL
 * 
 * This service provides transparent caching for detailed item data to improve
 * performance when users repeatedly view the same items. It uses Redis for
 * storage with configurable TTL and graceful fallback on cache failures.
 */
export class DetailedItemCache {
  private config: DetailedItemCacheConfig;

  constructor(config: Partial<DetailedItemCacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Generate a cache key for a detailed item
   */
  private getCacheKey(restaurantName: string, itemId: string): string {
    return `${this.config.keyPrefix}:${restaurantName}:${itemId}`;
  }

  /**
   * Get a detailed item from cache
   * @param restaurantName - The restaurant name from the URL
   * @param itemId - The item ID
   * @returns The cached detailed item or null if not found/expired
   */
  async get(restaurantName: string, itemId: string): Promise<DetailedItem | null> {
    if (!this.config.enabled) {
      return null;
    }

    try {
      const key = this.getCacheKey(restaurantName, itemId);
      const cachedData = await redis.get(key);

      if (!cachedData) {
        logger.trace({ restaurantName, itemId }, "Cache miss for detailed item");
        return null;
      }

      const detailedItem = JSON.parse(cachedData) as DetailedItem;
      logger.trace({ restaurantName, itemId }, "Cache hit for detailed item");
      return detailedItem;
    } catch (error) {
      logger.warn(
        { restaurantName, itemId, error },
        "Failed to get detailed item from cache, falling back to API"
      );
      return null;
    }
  }

  /**
   * Store a detailed item in cache
   * @param restaurantName - The restaurant name from the URL
   * @param itemId - The item ID
   * @param detailedItem - The detailed item data to cache
   */
  async set(restaurantName: string, itemId: string, detailedItem: DetailedItem): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const key = this.getCacheKey(restaurantName, itemId);
      const serializedData = JSON.stringify(detailedItem);

      await redis.setex(key, this.config.ttlSeconds, serializedData);

      logger.trace(
        { restaurantName, itemId, ttl: this.config.ttlSeconds },
        "Cached detailed item"
      );
    } catch (error) {
      logger.warn(
        { restaurantName, itemId, error },
        "Failed to cache detailed item, continuing without cache"
      );
      // Don't throw - caching failures should not break the application
    }
  }

  /**
   * Remove a specific item from cache
   * @param restaurantName - The restaurant name from the URL
   * @param itemId - The item ID
   */
  async invalidate(restaurantName: string, itemId: string): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const key = this.getCacheKey(restaurantName, itemId);
      await redis.del(key);

      logger.trace({ restaurantName, itemId }, "Invalidated cached detailed item");
    } catch (error) {
      logger.warn(
        { restaurantName, itemId, error },
        "Failed to invalidate cached detailed item"
      );
    }
  }

  /**
   * Clear all cached items for a specific restaurant
   * @param restaurantName - The restaurant name from the URL
   */
  async invalidateRestaurant(restaurantName: string): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const pattern = `${this.config.keyPrefix}:${restaurantName}:*`;
      const keys = await redis.keys(pattern);

      if (keys.length > 0) {
        await redis.del(...keys);
        logger.trace({ restaurantName, count: keys.length }, "Invalidated all cached detailed items for restaurant");
      }
    } catch (error) {
      logger.warn(
        { restaurantName, error },
        "Failed to invalidate cached detailed items for restaurant"
      );
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  async getStats(restaurantName?: string): Promise<{
    totalKeys: number;
    restaurantKeys?: number;
  }> {
    try {
      const allPattern = `${this.config.keyPrefix}:*`;
      const allKeys = await redis.keys(allPattern);

      const stats = {
        totalKeys: allKeys.length,
      };

      if (restaurantName) {
        const restaurantPattern = `${this.config.keyPrefix}:${restaurantName}:*`;
        const restaurantKeys = await redis.keys(restaurantPattern);
        return {
          ...stats,
          restaurantKeys: restaurantKeys.length,
        };
      }

      return stats;
    } catch (error) {
      logger.warn({ error }, "Failed to get cache stats");
      return { totalKeys: 0 };
    }
  }
}

/**
 * Default cache instance with standard configuration
 */
export const detailedItemCache = new DetailedItemCache();
