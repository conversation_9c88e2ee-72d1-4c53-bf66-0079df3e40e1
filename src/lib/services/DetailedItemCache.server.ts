import type { DetailedItem } from "$lib/types";
import { redis } from "$lib/server/redis.server.ts";
import { logger } from "$lib/logger/logger.svelte.ts";
import { publicConfig } from "../../publicConfig.ts";

/**
 * Configuration for the detailed item cache
 */
export interface DetailedItemCacheConfig {
  /** TTL in seconds for cached items (default: 300 = 5 minutes) */
  ttlSeconds: number;
  /** Whether caching is enabled (default: true) */
  enabled: boolean;
  /** Key prefix for Redis keys (default: "detailed_item") */
  keyPrefix: string;
}

/**
 * Default cache configuration
 */
const DEFAULT_CONFIG: DetailedItemCacheConfig = {
  ttlSeconds: publicConfig.cache.ttlSeconds,
  enabled: publicConfig.cache.enabled,
  keyPrefix: "detailed_item",
};

/**
 * Simple and intuitive cache service for detailed items with short TTL
 * 
 * This service provides transparent caching for detailed item data to improve
 * performance when users repeatedly view the same items. It uses Redis for
 * storage with configurable TTL and graceful fallback on cache failures.
 */
export class DetailedItemCache {
  private config: DetailedItemCacheConfig;

  constructor(config: Partial<DetailedItemCacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Generate a cache key for a detailed item
   */
  private getCacheKey(deviceId: string, itemId: string): string {
    return `${this.config.keyPrefix}:${deviceId}:${itemId}`;
  }

  /**
   * Get a detailed item from cache
   * @param deviceId - The device/restaurant ID
   * @param itemId - The item ID
   * @returns The cached detailed item or null if not found/expired
   */
  async get(deviceId: string, itemId: string): Promise<DetailedItem | null> {
    if (!this.config.enabled) {
      return null;
    }

    try {
      const key = this.getCacheKey(deviceId, itemId);
      const cachedData = await redis.get(key);

      if (!cachedData) {
        logger.trace({ deviceId, itemId }, "Cache miss for detailed item");
        return null;
      }

      const detailedItem = JSON.parse(cachedData) as DetailedItem;
      logger.trace({ deviceId, itemId }, "Cache hit for detailed item");
      return detailedItem;
    } catch (error) {
      logger.warn(
        { deviceId, itemId, error },
        "Failed to get detailed item from cache, falling back to API"
      );
      return null;
    }
  }

  /**
   * Store a detailed item in cache
   * @param deviceId - The device/restaurant ID
   * @param itemId - The item ID
   * @param detailedItem - The detailed item data to cache
   */
  async set(deviceId: string, itemId: string, detailedItem: DetailedItem): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const key = this.getCacheKey(deviceId, itemId);
      const serializedData = JSON.stringify(detailedItem);

      await redis.setex(key, this.config.ttlSeconds, serializedData);

      logger.trace(
        { deviceId, itemId, ttl: this.config.ttlSeconds },
        "Cached detailed item"
      );
    } catch (error) {
      logger.warn(
        { deviceId, itemId, error },
        "Failed to cache detailed item, continuing without cache"
      );
      // Don't throw - caching failures should not break the application
    }
  }

  /**
   * Remove a specific item from cache
   * @param deviceId - The device/restaurant ID
   * @param itemId - The item ID
   */
  async invalidate(deviceId: string, itemId: string): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const key = this.getCacheKey(deviceId, itemId);
      await redis.del(key);

      logger.trace({ deviceId, itemId }, "Invalidated cached detailed item");
    } catch (error) {
      logger.warn(
        { deviceId, itemId, error },
        "Failed to invalidate cached detailed item"
      );
    }
  }

  /**
   * Clear all cached items for a specific device/restaurant
   * @param deviceId - The device/restaurant ID
   */
  async invalidateDevice(deviceId: string): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const pattern = `${this.config.keyPrefix}:${deviceId}:*`;
      const keys = await redis.keys(pattern);

      if (keys.length > 0) {
        await redis.del(...keys);
        logger.trace({ deviceId, count: keys.length }, "Invalidated all cached detailed items for device");
      }
    } catch (error) {
      logger.warn(
        { deviceId, error },
        "Failed to invalidate cached detailed items for device"
      );
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  async getStats(deviceId?: string): Promise<{
    totalKeys: number;
    deviceKeys?: number;
  }> {
    try {
      const allPattern = `${this.config.keyPrefix}:*`;
      const allKeys = await redis.keys(allPattern);

      const stats = {
        totalKeys: allKeys.length,
      };

      if (deviceId) {
        const devicePattern = `${this.config.keyPrefix}:${deviceId}:*`;
        const deviceKeys = await redis.keys(devicePattern);
        return {
          ...stats,
          deviceKeys: deviceKeys.length,
        };
      }

      return stats;
    } catch (error) {
      logger.warn({ error }, "Failed to get cache stats");
      return { totalKeys: 0 };
    }
  }
}

/**
 * Default cache instance with standard configuration
 */
export const detailedItemCache = new DetailedItemCache();
